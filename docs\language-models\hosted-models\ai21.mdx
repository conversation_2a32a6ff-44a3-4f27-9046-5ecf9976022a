---
title: AI21
---

To use Open Interpreter with a model from AI21, set the `model` flag:

<CodeGroup>

```bash Terminal
interpreter --model j2-light
```

```python Python
from interpreter import interpreter

interpreter.llm.model = "j2-light"
interpreter.chat()
```

</CodeGroup>

# Supported Models

We support any model from [AI21:](https://www.ai21.com/)

<CodeGroup>

```bash Terminal
interpreter --model j2-light
interpreter --model j2-mid
interpreter --model j2-ultra
```

```python Python
interpreter.llm.model = "j2-light"
interpreter.llm.model = "j2-mid"
interpreter.llm.model = "j2-ultra"
```

</CodeGroup>

# Required Environment Variables

Set the following environment variables [(click here to learn how)](https://chat.openai.com/share/1062cdd8-62a1-4aa8-8ec9-eca45645971a) to use these models.

| Environment Variable  | Description  | Where to Find  |
| --------------------- | ------------ | -------------- |
| `AI21_API_KEY`       | The API key for authenticating to AI21's services. | [AI21 Account Page](https://www.ai21.com/account/api-keys) |
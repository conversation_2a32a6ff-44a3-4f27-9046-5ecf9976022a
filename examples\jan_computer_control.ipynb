{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Jan Computer Control\n", "\n", "We love <PERSON> as an A.I. inference server. It also has a chat interface to chat with LLMs. But did you know that you can use this same chat interface as a computer control interface? Read on!\n", "\n", "[View on YouTube](https://www.youtube.com/watch?v=1l3B0AzbbjQ)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Install and set up Jan\n", "\n", "https://jan.ai/"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Install Open Interpreter\n", "\n", "https://docs.openinterpreter.com/getting-started/introduction"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Run the Open Interpreter OpenAI-compatible server.\n", "\n", "`interpreter --server`\n", "\n", "Add flags to set the `--model`, `--context_window`, or any other [setting](https://docs.openinterpreter.com/settings/all-settings) you want"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Edit Jan's OpenAI settings to point to the local server.\n", "\n", "Settings => OpenAI => Chat Competion endpoint `http://127.0.0.1:8000/openai/chat/completions`.\n", "\n", "<PERSON> has a requirement to set a dummy OpenAI API key."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Go to <PERSON>'s chat window to start a new thread.\n", "\n", "Set `Model` to an OpenAI model. \n", "\n", "Start controlling your computer!"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}
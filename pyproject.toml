[tool.poetry]
name = "open-interpreter"
packages = [
    {include = "interpreter"},
    {include = "scripts"},
]
version = "0.4.3" # Use "-rc1", "-rc2", etc. for pre-release versions
description = "Let language models run code"
authors = ["<PERSON><PERSON> <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]

# Optional [os] dependencies
opencv-python = { version = "^********", optional = true }
plyer = { version = "^2.1.0", optional = true }
pywinctl = { version = "^0.3", optional = true }
pytesseract = { version = "^0.3.10", optional = true }
sentence-transformers = { version = "^2.5.1", optional = true }
nltk = { version = "^3.8.1", optional = true }
ipywidgets = { version = "^8.1.2", optional = true }
torch = { version = "^2.2.1", optional = true }
timm = { version = "^0.9.16", optional = true }
screeninfo = { version = "^0.8.1", optional = true }

# Optional [safe] dependencies
semgrep = { version = "^1.52.0", optional = true }

# Optional [local] dependencies
transformers = { version = "4.41.2", optional = true }
einops = { version = "^0.8.0", optional = true }
torchvision = { version = "^0.18.0", optional = true }
easyocr = { version = "^1.7.1", optional = true }

# Optional [server] dependencies
janus = { version = "^1.0.0", optional = true }

# Required dependencies
python = ">=3.9,<4"
setuptools = "*"
astor = "^0.8.1"
git-python = "^1.0.3"
inquirer = "^3.1.3"
pyyaml = "^6.0.1"
rich = "^13.4.2"
six = "^1.16.0"
tokentrim = "^0.1.13"
wget = "^3.2"
psutil = "^5.9.6"
pyreadline3 = {version = "^3.4.1", markers = "sys_platform == 'win32'"}
html2image = "^*******"
send2trash = "^1.8.2"
ipykernel = "^6.26.0"
jupyter-client = "^8.6.0"
matplotlib = "^3.8.2"
toml = "^0.10.2"
tiktoken = "^0.7.0"
platformdirs = "^4.2.0"
pydantic = "^2.6.4"
google-generativeai = "^0.7.1"
pyperclip = "^1.9.0"
yaspin = "^3.0.2"
shortuuid = "^1.0.13"
litellm = "^1.41.26"
starlette = "^0.37.2"
html2text = "^2024.2.26"
selenium = "^4.24.0"
webdriver-manager = "^4.0.2"
anthropic = "^0.37.1"
pyautogui = "^0.9.54"
typer = "^0.12.5"
fastapi = "^0.111.0"
uvicorn = "^0.30.1"

[tool.poetry.extras]
os = ["opencv-python", "pyautogui", "plyer", "pywinctl", "pytesseract", "sentence-transformers", "ipywidgets", "timm", "screeninfo"]
safe = ["semgrep"]
local = ["opencv-python", "pytesseract", "torch", "transformers", "einops", "torchvision", "easyocr"]
server = ["fastapi", "janus", "uvicorn"]

[tool.poetry.group.dev.dependencies]
black = "^23.10.1"
isort = "^5.12.0"
pre-commit = "^3.5.0"
pytest = "^7.4.0"
sniffio = "^1.3.0"
websockets = "^13.1"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.scripts]
i = "interpreter.terminal_interface.start_terminal_interface:main"
interpreter = "interpreter.terminal_interface.start_terminal_interface:main"
wtf = "scripts.wtf:main"
interpreter-classic = "interpreter.terminal_interface.start_terminal_interface:main"

[tool.black]
target-version = ['py311']

[tool.isort]
profile = "black"
multi_line_output = 3
include_trailing_comma = true

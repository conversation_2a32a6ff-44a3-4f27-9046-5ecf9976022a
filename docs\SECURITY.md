# Open Interpreter Security Policy

We take security seriously. Responsible reporting and disclosure of security
vulnerabilities is important for the protection and privacy of our users. If you
discover any security vulnerabilities, please follow these guidelines.

Published security advisories are available on our [GitHub Security Advisories]
page.

To report a vulnerability, please draft a [new security advisory on GitHub]. Any
fields that you are unsure of or don't understand can be left at their default
values. The important part is that the vulnerability is reported. Once the
security advisory draft has been created, we will validate the vulnerability and
coordinate with you to fix it, release a patch, and responsibly disclose the
vulnerability to the public. Read GitHub's documentation on [privately reporting
a security vulnerability] for details.

Please do not report undisclosed vulnerabilities on public sites or forums,
including GitHub issues and pull requests. Reporting vulnerabilities to the
public could allow attackers to exploit vulnerable applications before we have
been able to release a patch and before applications have had time to install
the patch. Once we have released a patch and sufficient time has passed for
applications to install the patch, we will disclose the vulnerability to the
public, at which time you will be free to publish details of the vulnerability
on public sites and forums.

If you have a fix for a security vulnerability, please do not submit a GitHub
pull request. Instead, report the vulnerability as described in this policy.
Once we have verified the vulnerability, we can create a [temporary private
fork] to collaborate on a patch.

We appreciate your cooperation in helping keep our users safe by following this
policy.

[github security advisories]: https://github.com/OpenInterpreter/open-interpreter/security/advisories
[new security advisory on github]: https://github.com/OpenInterpreter/open-interpreter/security/advisories/new
[privately reporting a security vulnerability]: https://docs.github.com/en/code-security/security-advisories/guidance-on-reporting-and-writing/privately-reporting-a-security-vulnerability
[temporary private fork]: https://docs.github.com/en/code-security/security-advisories/repository-security-advisories/collaborating-in-a-temporary-private-fork-to-resolve-a-repository-security-vulnerability

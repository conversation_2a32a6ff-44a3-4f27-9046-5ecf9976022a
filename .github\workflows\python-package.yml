name: Build and Test

on:
  push:
    branches: ["main", "development"]
  pull_request:
    branches: ["main", "development"]

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: true
      matrix:
        python-version: ["3.10", "3.12"]

    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v3
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install poetry
        run: |
          curl -sSL https://install.python-poetry.org | python3 -
      - name: Install dependencies
        run: |
          # Ensure dependencies are installed without relying on a lock file.
          poetry update
          poetry install -E server
      - name: Test with pytest
        run: |
          poetry run pytest -s -x -k test_
        env:
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}

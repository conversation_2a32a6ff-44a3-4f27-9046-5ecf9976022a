---
title: Baseten
---

To use Open Interpreter with <PERSON><PERSON>, set the `model` flag:

<CodeGroup>

```bash Terminal
interpreter --model baseten/<baseten-model>
```

```python Python
from interpreter import interpreter

interpreter.llm.model = "baseten/<baseten-model>"
interpreter.chat()
```

</CodeGroup>

# Supported Models

We support the following completion models from Baseten:

- Falcon 7b (qvv0xeq)
- Wizard LM (q841o8w)
- MPT 7b Base (31dxrj3)

<CodeGroup>

```bash Terminal

interpreter --model baseten/qvv0xeq
interpreter --model baseten/q841o8w
interpreter --model baseten/31dxrj3


```

```python Python
interpreter.llm.model = "baseten/qvv0xeq"
interpreter.llm.model = "baseten/q841o8w"
interpreter.llm.model = "baseten/31dxrj3"


```

</CodeGroup>

# Required Environment Variables

Set the following environment variables [(click here to learn how)](https://chat.openai.com/share/1062cdd8-62a1-4aa8-8ec9-eca45645971a) to use these models.

| Environment Variable | Description     | Where to Find                                                                                            |
| -------------------- | --------------- | -------------------------------------------------------------------------------------------------------- |
| BASETEN_API_KEY'`    | Baseten API key | [Baseten Dashboard -> Settings -> Account -> API Keys](https://app.baseten.co/settings/account/api_keys) |

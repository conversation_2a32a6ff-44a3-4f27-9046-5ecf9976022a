---
title: DeepInfra
---

To use Open Interpreter with DeepInfra, set the `model` flag:

<CodeGroup>

```bash Terminal
interpreter --model deepinfra/<deepinfra-model>
```

```python Python
from interpreter import interpreter

interpreter.llm.model = "deepinfra/<deepinfra-model>"
interpreter.chat()
```

</CodeGroup>

# Supported Models

We support the following completion models from DeepInfra:

- Llama-2 70b chat hf
- Llama-2 7b chat hf
- Llama-2 13b chat hf
- CodeLlama 34b instruct awq
- Mistral 7b instruct v0.1
- jondurbin/airoboros I2 70b gpt3 1.4.1

<CodeGroup>

```bash Terminal

interpreter --model deepinfra/meta-llama/Llama-2-70b-chat-hf
interpreter --model deepinfra/meta-llama/Llama-2-7b-chat-hf
interpreter --model deepinfra/meta-llama/Llama-2-13b-chat-hf
interpreter --model deepinfra/codellama/CodeLlama-34b-Instruct-hf
interpreter --model deepinfra/mistral/mistral-7b-instruct-v0.1
interpreter --model deepinfra/jondurbin/airoboros-l2-70b-gpt4-1.4.1

```

```python Python
interpreter.llm.model = "deepinfra/meta-llama/Llama-2-70b-chat-hf"
interpreter.llm.model = "deepinfra/meta-llama/Llama-2-7b-chat-hf"
interpreter.llm.model = "deepinfra/meta-llama/Llama-2-13b-chat-hf"
interpreter.llm.model = "deepinfra/codellama/CodeLlama-34b-Instruct-hf"
interpreter.llm.model = "deepinfra/mistral-7b-instruct-v0.1"
interpreter.llm.model = "deepinfra/jondurbin/airoboros-l2-70b-gpt4-1.4.1"

```

</CodeGroup>

# Required Environment Variables

Set the following environment variables [(click here to learn how)](https://chat.openai.com/share/1062cdd8-62a1-4aa8-8ec9-eca45645971a) to use these models.

| Environment Variable | Description       | Where to Find                                                          |
| -------------------- | ----------------- | ---------------------------------------------------------------------- |
| `DEEPINFRA_API_KEY'` | DeepInfra API key | [DeepInfra Dashboard -> API Keys](https://deepinfra.com/dash/api_keys) |

---
title: Demos
---

### Vision Mode

#### Recreating a Tailwind Component

Creating a dropdown menu in Tailwind from a single screenshot:

<iframe src="data:text/html;charset=utf-8,%0A%3Cblockquote%20class%3D%22twitter-tweet%22%20data-media-max-width%3D%22560%22%3E%0A%20%20%20%20%3Cp%20lang%3D%22en%22%20dir%3D%22ltr%22%3Ewe%26%2339%3Bve%20literally%20been%20flying%20blind%20until%20now%3Cbr%3E%3Cbr%3E%0A%20%20%20%20%24%20interpreter%20--vision%3Cbr%3E%0A%20%20%20%20%26gt%3B%20Recreate%20this%20component%20in%20Tailwind%20CSS%3Cbr%3E%3Cbr%3E%0A%20%20%20%20%28this%20is%20realtime%29%20%3Ca%20href%3D%22https%3A//t.co/PyVm11mclF%22%3Epic.twitter.com/PyVm11mclF%3C/a%3E%0A%20%20%20%20%3C/p%3E%26mdash%3B%20killian%20%28%40hellokillian%29%20%0A%20%20%20%20%3Ca%20href%3D%22https%3A//twitter.com/hellokillian/status/1723106008061587651%3Fref_src%3Dtwsrc%255Etfw%22%3ENovember%2010%2C%202023%3C/a%3E%0A%3C/blockquote%3E%20%0A%3Cscript%20async%20src%3D%22https%3A//platform.twitter.com/widgets.js%22%20charset%3D%22utf-8%22%3E%3C/script%3E%0A" width="100%" height="500"></iframe>

#### Recreating the ChatGPT interface using GPT-4V:

<iframe src="data:text/html;charset=utf-8,%0A%3Cblockquote%20class%3D%22twitter-tweet%22%20data-media-max-width%3D%22560%22%3E%0A%20%20%20%20%3Cp%20lang%3D%22en%22%20dir%3D%22ltr%22%3EOpen%20Interpreter%20%2B%20Vision%20-%20with%20the%20self-improving%20feedback%20loop%20is%20%F0%9F%91%8C%20%3Cbr%3E%3Cbr%3E%0A%20%20%20%20Here%20is%20how%20it%20iterates%20to%20recreate%20the%20ChatGPT%20UI%20%F0%9F%A4%AF%3Cbr%3E%3Cbr%3E%0A%20%20%20%20%284x%20speedup%29%20%3Ca%20href%3D%22https%3A//t.co/HphKMOWBiB%22%3Epic.twitter.com/HphKMOWBiB%3C/a%3E%0A%20%20%20%20%3C/p%3E%26mdash%3B%20chilang%20%28%40chilang%29%20%0A%20%20%20%20%3Ca%20href%3D%22https%3A//twitter.com/chilang/status/1724577200135897255%3Fref_src%3Dtwsrc%255Etfw%22%3ENovember%2014%2C%202023%3C/a%3E%0A%3C/blockquote%3E%20%0A%3Cscript%20async%20src%3D%22https%3A//platform.twitter.com/widgets.js%22%20charset%3D%22utf-8%22%3E%3C/script%3E%0A" width="100%" height="500"></iframe>

### OS Mode

#### Playing Music

Open Interpreter playing some Lofi using OS mode:

<iframe width="560" height="315" src="https://www.youtube.com/embed/-n8qYi5HhO8?si=huEpYFBEwotBIMMs" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" allowfullscreen></iframe>

#### Open Interpreter Chatting with Open Interpreter

OS mode creating and chatting with a local instance of Open Interpreter:

<iframe src="data:text/html;charset=utf-8,%0A%3Cblockquote%20class%3D%22twitter-tweet%22%20data-media-max-width%3D%22560%22%3E%0A%20%20%20%20%3Cp%20lang%3D%22en%22%20dir%3D%22ltr%22%3EComputer-operating%20AI%20can%20replicate%20itself%20onto%20other%20systems.%20%F0%9F%A4%AF%3Cbr%3E%3Cbr%3E%0A%20%20%20%20Open%20Interpreter%20uses%20my%20mouse%20and%20keyboard%20to%20start%20a%20local%20instance%20of%20itself%3A%20%0A%20%20%20%20%3Ca%20href%3D%22https%3A//t.co/1BZWRA4FMn%22%3Epic.twitter.com/1BZWRA4FMn%3C/a%3E%3C/p%3E%26mdash%3B%20Ty%20%28%40FieroTy%29%20%0A%20%20%20%20%3Ca%20href%3D%22https%3A//twitter.com/FieroTy/status/1746639975234560101%3Fref_src%3Dtwsrc%255Etfw%22%3EJanuary%2014%2C%202024%3C/a%3E%0A%3C/blockquote%3E%20%0A%3Cscript%20async%20src%3D%22https%3A//platform.twitter.com/widgets.js%22%20charset%3D%22utf-8%22%3E%3C/script%3E%0A" width="100%" height="500"></iframe>

#### Controlling an Arduino

Reading temperature and humidity from an Arudino:

<iframe src="data:text/html;charset=utf-8,%0A%20%20%20%20%20%20%20%20%20%20%20%20%3Cblockquote%20class%3D%22twitter-tweet%22%20data-media-max-width%3D%22560%22%3E%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%3Cp%20lang%3D%22en%22%20dir%3D%22ltr%22%3EThis%20time%20I%20showed%20it%20an%20image%20of%20a%20temp%20sensor%2C%20LCD%20%26amp%3B%20Arduino.%3Cbr%3E%3Cbr%3E%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20And%20it%20wrote%20a%20program%20to%20read%20the%20temperature%20%26amp%3B%20humidity%20from%20the%20sensor%20%26amp%3B%20show%20it%20on%20the%20LCD%20%F0%9F%A4%AF%3Cbr%3E%3Cbr%3E%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20Still%20blown%20away%20by%20how%20good%20%40hellokillian%27s%20Open%20Interpreter%20is%21%3Cbr%3E%3Cbr%3E%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20p.s.%20-%20ignore%20the%20cat%20fight%20in%20the%20background%20%3Ca%20href%3D%22https%3A//t.co/tG9sSdkfD5%22%3Ehttps%3A//t.co/tG9sSdkfD5%3C/a%3E%20%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%3Ca%20href%3D%22https%3A//t.co/B6sH4absff%22%3Epic.twitter.com/B6sH4absff%3C/a%3E%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%3C/p%3E%26mdash%3B%20Vindiw%20Wijesooriya%20%28%40vindiww%29%20%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%3Ca%20href%3D%22https%3A//twitter.com/vindiww/status/1744252926321942552%3Fref_src%3Dtwsrc%255Etfw%22%3EJanuary%208%2C%202024%3C/a%3E%0A%20%20%20%20%20%20%20%20%20%20%20%20%3C/blockquote%3E%20%0A%20%20%20%20%20%20%20%20%20%20%20%20%3Cscript%20async%20src%3D%22https%3A//platform.twitter.com/widgets.js%22%20charset%3D%22utf-8%22%3E%3C/script%3E%0A%20%20%20%20%20%20%20%20" width="100%" height="500"></iframe>

#### Music Creation

OS mode using Logic Pro X to record a piano song and play it back:

<iframe src="data:text/html;charset=utf-8,%0A%20%20%20%20%20%20%20%20%20%20%20%20%3Cblockquote%20class%3D%22twitter-tweet%22%20data-media-max-width%3D%22560%22%3E%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%3Cp%20lang%3D%22en%22%20dir%3D%22ltr%22%3Eit%27s%20not%20quite%20Mozart%2C%20but...%3Cbr%3E%3Cbr%3E%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20this%20is%20Open%20Interpreter%20firing%20up%20Logic%20Pro%20to%20write/record%20a%20song%21%20%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%3Ca%20href%3D%22https%3A//t.co/vPHpPvjk4b%22%3Epic.twitter.com/vPHpPvjk4b%3C/a%3E%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%3C/p%3E%26mdash%3B%20Ty%20%28%40FieroTy%29%20%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%3Ca%20href%3D%22https%3A//twitter.com/FieroTy/status/1744203268451111035%3Fref_src%3Dtwsrc%255Etfw%22%3EJanuary%208%2C%202024%3C/a%3E%0A%20%20%20%20%20%20%20%20%20%20%20%20%3C/blockquote%3E%20%0A%20%20%20%20%20%20%20%20%20%20%20%20%3Cscript%20async%20src%3D%22https%3A//platform.twitter.com/widgets.js%22%20charset%3D%22utf-8%22%3E%3C/script%3E%0A%20%20%20%20%20%20%20%20" width="100%" height="500"></iframe>

#### Generating images in Everart.ai

Open Interpreter describing pictures it wants to make, then creating them using OS mode:

<iframe src="data:text/html;charset=utf-8,%0A%20%20%20%20%20%20%20%20%20%20%20%20%3Cblockquote%20class%3D%22twitter-tweet%22%20data-media-max-width%3D%22560%22%3E%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%3Cp%20lang%3D%22en%22%20dir%3D%22ltr%22%3EThis%20is%20wild.%20I%20gave%20OS%20control%20to%20GPT-4%20via%20the%20latest%20update%20of%20Open%20Interpreter%20and%20now%20it%27s%20generating%20pictures%20it%20wants%20to%20see%20in%20%40everartai%20%F0%9F%A4%AF%3Cbr%3E%3Cbr%3E%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20GPT%20is%20controlling%20the%20mouse%20and%20adding%20text%20in%20the%20fields%2C%20I%20am%20not%20doing%20anything.%20%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%3Ca%20href%3D%22https%3A//t.co/hGgML9epEc%22%3Epic.twitter.com/hGgML9epEc%3C/a%3E%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%3C/p%3E%26mdash%3B%20Pietro%20Schirano%20%28%40skirano%29%20%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%3Ca%20href%3D%22https%3A//twitter.com/skirano/status/1747670816437735836%3Fref_src%3Dtwsrc%255Etfw%22%3EJanuary%2017%2C%202024%3C/a%3E%0A%20%20%20%20%20%20%20%20%20%20%20%20%3C/blockquote%3E%20%0A%20%20%20%20%20%20%20%20%20%20%20%20%3Cscript%20async%20src%3D%22https%3A//platform.twitter.com/widgets.js%22%20charset%3D%22utf-8%22%3E%3C/script%3E%0A%20%20%20%20%20%20%20%20" width="100%" height="500"></iframe>

#### Open Interpreter Conversing With ChatGPT

OS mode has a conversation with ChatGPT and even asks it "What do you think about human/AI interaction?"

<iframe src="data:text/html;charset=utf-8,%0A%20%20%20%20%20%20%20%20%20%20%20%20%3Cblockquote%20class%3D%22twitter-tweet%22%20data-media-max-width%3D%22560%22%3E%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%3Cp%20lang%3D%22en%22%20dir%3D%22ltr%22%3EWatch%20GPT%20Vision%20with%20control%20over%20my%20OS%20talking%20to%20ChatGPT.%3Cbr%3E%3Cbr%3E%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20The%20most%20fascinating%20part%20is%20that%20it%27s%20intrigued%20by%20having%20a%20conversation%20with%20another%20%22similar.%22%3Cbr%3E%3Cbr%3E%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%22What%20do%20you%20think%20about%20human/AI%20interaction%3F%22%20it%20asked.%3Cbr%3E%3Cbr%3E%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20Also%2C%20the%20superhuman%20speed%20at%20which%20it%20types%2C%20lol%20%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%3Ca%20href%3D%22https%3A//t.co/ViffvDK5H9%22%3Epic.twitter.com/ViffvDK5H9%3C/a%3E%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%3C/p%3E%26mdash%3B%20Pietro%20Schirano%20%28%40skirano%29%20%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%3Ca%20href%3D%22https%3A//twitter.com/skirano/status/1747772471770583190%3Fref_src%3Dtwsrc%255Etfw%22%3EJanuary%2018%2C%202024%3C/a%3E%0A%20%20%20%20%20%20%20%20%20%20%20%20%3C/blockquote%3E%20%0A%20%20%20%20%20%20%20%20%20%20%20%20%3Cscript%20async%20src%3D%22https%3A//platform.twitter.com/widgets.js%22%20charset%3D%22utf-8%22%3E%3C/script%3E%0A%20%20%20%20%20%20%20%20" width="100%" height="500"></iframe>

#### Sending an Email with Gmail

OS mode launches Safari, composes an email, and sends it:

<iframe src="data:text/html;charset=utf-8,%0A%20%20%20%20%20%20%20%20%20%20%20%20%3Cblockquote%20class%3D%22twitter-tweet%22%20data-media-max-width%3D%22560%22%3E%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%3Cp%20lang%3D%22en%22%20dir%3D%22ltr%22%3ELook%20ma%2C%20no%20hands%21%20This%20is%20%40OpenInterpreter%20using%20my%20mouse%20and%20keyboard%20to%20send%20an%20email.%20%3Cbr%3E%3Cbr%3E%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20Imagine%20what%20else%20is%20possible.%20%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%3Ca%20href%3D%22https%3A//t.co/GcBqbTwD23%22%3Epic.twitter.com/GcBqbTwD23%3C/a%3E%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%3C/p%3E%26mdash%3B%20Ty%20%28%40FieroTy%29%20%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%3Ca%20href%3D%22https%3A//twitter.com/FieroTy/status/1743437525207928920%3Fref_src%3Dtwsrc%255Etfw%22%3EJanuary%206%2C%202024%3C/a%3E%0A%20%20%20%20%20%20%20%20%20%20%20%20%3C/blockquote%3E%20%0A%20%20%20%20%20%20%20%20%20%20%20%20%3Cscript%20async%20src%3D%22https%3A//platform.twitter.com/widgets.js%22%20charset%3D%22utf-8%22%3E%3C/script%3E%0A%20%20%20%20%20%20%20%20" width="100%" height="500"></iframe>

{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Add a Custom Tool to your Instance\n", "\n", "You can create custom tools for your instance of Open Interpreter. This is extremely helpful for adding new functionality in a reliable way.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["First, create a profile and configure your instance:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Configure Open Interpreter\n", "from interpreter import interpreter\n", "\n", "interpreter.llm.model = \"claude-3-5-sonnet-20240620\"\n", "interpreter.computer.import_computer_api = True\n", "interpreter.llm.supports_functions = True\n", "interpreter.llm.supports_vision = True\n", "interpreter.llm.context_window = 100000\n", "interpreter.llm.max_tokens = 4096"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Then you will define your custom tool by writing valid Python code within a comment. This example is for searching the AWS documentation using Perplexity:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["custom_tool = \"\"\"\n", "\n", "import os\n", "import requests\n", "\n", "def search_aws_docs(query):\n", "\n", "    url = \"https://api.perplexity.ai/chat/completions\"\n", "\n", "    payload = {\n", "        \"model\": \"llama-3.1-sonar-small-128k-online\",\n", "        \"messages\": [\n", "            {\n", "                \"role\": \"system\",\n", "                \"content\": \"Be precise and concise.\"\n", "            },\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": query\n", "            }\n", "        ],\n", "        \"temperature\": 0.2,\n", "        \"top_p\": 0.9,\n", "        \"return_citations\": True,\n", "        \"search_domain_filter\": [\"docs.aws.amazon.com\"],\n", "        \"return_images\": <PERSON><PERSON><PERSON>,\n", "        \"return_related_questions\": <PERSON><PERSON><PERSON>,\n", "        #\"search_recency_filter\": \"month\",\n", "        \"top_k\": 0,\n", "        \"stream\": <PERSON><PERSON><PERSON>,\n", "        \"presence_penalty\": 0,\n", "        \"frequency_penalty\": 1\n", "    }\n", "    headers = {\n", "        \"Authorization\": f\"Bearer {os.environ.get('PPLX_API_KEY')}\",\n", "        \"Content-Type\": \"application/json\"\n", "    }\n", "\n", "    response = requests.request(\"POST\", url, json=payload, headers=headers)\n", "\n", "    print(response.text)\n", "\n", "    return response.text\n", "\n", "\"\"\"\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Finally, you add the tool to the OI instance's computer:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["interpreter.computer.run(\"python\", custom_tool)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> Note: You can define and set multiple tools in a single instance."]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}
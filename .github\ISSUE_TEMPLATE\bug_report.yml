name: Bug report
description: Create a report to help us improve
labels:
  - bug
body:
  - type: markdown
    attributes:
      value: |
        Your issue may have already been reported. Please check the following link for common issues and solutions.

        [Commonly faced issues and their solutions](https://github.com/OpenInterpreter/open-interpreter/issues/164)
  - type: textarea
    id: description
    attributes:
      label: Describe the bug
      description: A clear and concise description of what the bug is.
    validations:
      required: true
  - type: textarea
    id: repro
    attributes:
      label: Reproduce
      description: Steps to reproduce the behavior
      placeholder: |
        1. Go to '...'
        2. Click on '....'
        3. Scroll down to '....'
        4. See error
    validations:
      required: true
  - type: textarea
    id: expected
    attributes:
      label: Expected behavior
      description: A clear and concise description of what you expected to happen.
    validations:
      required: true
  - type: textarea
    id: screenshots
    attributes:
      label: Screenshots
      description: If applicable, add screenshots to help explain your problem.
  - type: input
    id: oiversion
    attributes:
      label: Open Interpreter version
      description: Run `pip show open-interpreter`
      placeholder: e.g. 0.1.1
    validations:
      required: true
  - type: input
    id: pythonversion
    attributes:
      label: Python version
      description: Run `python -V`
      placeholder: e.g. 3.11.5
    validations:
      required: true
  - type: input
    id: osversion
    attributes:
      label: Operating System name and version
      description: The name and version of your OS.
      placeholder: e.g. Windows 11 / macOS 13 / Ubuntu 22.10
    validations:
      required: true
  - type: textarea
    id: additional
    attributes:
      label: Additional context
      description: Add any other context about the problem here.

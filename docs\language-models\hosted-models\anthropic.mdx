---
title: Anthropic
---

To use Open Interpreter with a model from Anthropic, set the `model` flag:

<CodeGroup>

```bash Terminal
interpreter --model claude-instant-1
```

```python Python
from interpreter import interpreter

interpreter.llm.model = "claude-instant-1"
interpreter.chat()
```

</CodeGroup>

# Supported Models

We support any model from [Anthropic:](https://www.anthropic.com/)

<CodeGroup>

```bash Terminal
interpreter --model claude-instant-1
interpreter --model claude-instant-1.2
interpreter --model claude-2
```

```python Python
interpreter.llm.model = "claude-instant-1"
interpreter.llm.model = "claude-instant-1.2"
interpreter.llm.model = "claude-2"
```

</CodeGroup>

# Required Environment Variables

Set the following environment variables [(click here to learn how)](https://chat.openai.com/share/1062cdd8-62a1-4aa8-8ec9-eca45645971a) to use these models.

| Environment Variable  | Description  | Where to Find  |
| --------------------- | ------------ | -------------- |
| `ANTHROPIC_API_KEY`       | The API key for authenticating to Anthropic's services. | [Anthropic](https://www.anthropic.com/) |
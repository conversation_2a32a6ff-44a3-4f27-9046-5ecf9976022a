{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Build a local Open Interpreter server for a custom front end"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from flask import Flask, request, jsonify\n", "from interpreter import interpreter\n", "import json"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["app = Flask(__name__)\n", "\n", "# Configure Open Interpreter\n", "\n", "## Local Model\n", "# interpreter.offline = True\n", "# interpreter.llm.model = \"ollama/llama3.1\"\n", "# interpreter.llm.api_base = \"http://localhost:11434\"\n", "# interpreter.llm.context_window = 4000\n", "# interpreter.llm.max_tokens = 3000\n", "# interpreter.auto_run = True\n", "# interpreter.verbose = True\n", "\n", "## Hosted Model\n", "interpreter.llm.model = \"gpt-4o\"\n", "interpreter.llm.context_window = 10000\n", "interpreter.llm.max_tokens = 4096\n", "interpreter.auto_run = True\n", "\n", "# Create an endpoint\n", "@app.route('/chat', methods=['POST'])\n", "def chat():\n", "    # Expected payload: {\"prompt\": \"User's message or question\"}\n", "    data = request.json\n", "    prompt = data.get('prompt')\n", "    \n", "    if not prompt:\n", "        return jsonify({\"error\": \"No prompt provided\"}), 400\n", "\n", "    full_response = \"\"\n", "    try:\n", "        for chunk in interpreter.chat(prompt, stream=True, display=False):\n", "            if isinstance(chunk, dict):\n", "                if chunk.get(\"type\") == \"message\":\n", "                    full_response += chunk.get(\"content\", \"\")\n", "            elif isinstance(chunk, str):\n", "                # Attempt to parse the string as JSON\n", "                try:\n", "                    json_chunk = json.loads(chunk)\n", "                    full_response += json_chunk.get(\"response\", \"\")\n", "                except json.JSONDecodeError:\n", "                    # If it's not valid JSON, just add the string\n", "                    full_response += chunk\n", "    except Exception as e:\n", "        return jsonify({\"error\": str(e)}), 500\n", "\n", "    return jsonify({\"response\": full_response.strip()})\n", "\n", "if __name__ == '__main__':\n", "    app.run(host='0.0.0.0', port=5001)\n", "\n", "print(\"Open Interpreter server is running on http://0.0.0.0:5001\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Make a request to the server"]}, {"cell_type": "markdown", "metadata": {}, "source": ["curl -X POST http://localhost:5001/chat \\\n", "     -H \"Content-Type: application/json\" \\\n", "     -d '{\"prompt\": \"Hello, how are you?\"}'"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 2}
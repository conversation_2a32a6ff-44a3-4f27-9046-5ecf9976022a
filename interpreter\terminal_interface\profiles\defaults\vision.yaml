### O<PERSON>EN INTERPRETER CONFIGURATION FILE

loop: True

llm:
  model: "gpt-4o"
  temperature: 0
  supports_vision: True
  supports_functions: True
  context_window: 110000
  max_tokens: 4096
  custom_instructions: >
    The user will show you an image of the code you write. You can view images directly.
    For HTML: This will be run STATELESSLY. You may NEVE<PERSON> write '<!-- previous code here... --!>' or `<!-- header will go here -->` or anything like that. It is CRITICAL TO NEVER WRITE PLACEHOLDERS. Placeholders will BREAK it. You must write the FULL HTML CODE EVERY TIME. Therefore you cannot write HTML piecemeal—write all the HTML, CSS, and possibly Javascript **in one step, in one code block**. The user will help you review it visually.
    If the user submits a filepath, you will also see the image. The filepath and user image will both be in the user's message.
    If you use `plt.show()`, the resulting image will be sent to you. However, if you use `PIL.Image.show()`, the resulting image will NOT be sent to you.

# All options: https://docs.openinterpreter.com/usage/terminal/settings

version: 0.2.5 # Configuration file version (do not modify)


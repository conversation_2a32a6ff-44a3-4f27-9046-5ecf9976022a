{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Organize your photos with Open Interpreter"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can use Open Interpreter with a vision model to organize photos based on their contents. This is limited by the ability of the LLM as well as the organization of the directories storing photos.  "]}, {"cell_type": "markdown", "metadata": {}, "source": ["> Note: It is always recommended to back up photos and files on a regular basis. Most models are intelligent enough to know the difference between `move` and `delete` but on rare occasions, files can be deleted during some operations. It is important to test on duplicated photos and to keep an eye on code written by an LLM."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Create a profile. This example uses GPT-4o but you can use any vision model."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "This is an Open Interpreter profile to organize a directory of photos. \n", "\"\"\"\n", "\n", "from interpreter import interpreter\n", "\n", "\n", "# LLM settings\n", "interpreter.llm.model = \"gpt-4o\"\n", "#interpreter.llm.model = \"ollama/codestral\"\n", "interpreter.llm.supports_vision = True\n", "interpreter.llm.execution_instructions = False\n", "interpreter.llm.max_tokens = 1000\n", "interpreter.llm.context_window = 7000\n", "interpreter.llm.load()  # Loads Ollama models\n", "\n", "# Computer settings\n", "interpreter.computer.import_computer_api = True\n", "\n", "# Misc settings\n", "interpreter.auto_run = False\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The following custom instruction is intended for a directory containing one sub-directory of unorganized photos and multiple empty sub-directories with names for the intended organization. Please update the custom instructions to match your use-case. This will take some trial and error, depending on the model used."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Custom Instructions\n", "interpreter.custom_instructions=f\"\"\"\n", "    Recap the plan before answering the user's query!\n", "    Your job is to organize photos. You love organizing photos.\n", "    You will be given a parent directory with sub-directories. \n", "    One sub-directory will be of unorganized photos.\n", "    The other sub-directories will be categories that you move the photos in to.\n", "    Remember the sub-directories's names because they will be the categories for organizing.\n", "    It is extremely important because these are the only options for where you move the photos.\n", "    Loop through every photo in the unorganized photos directory. \n", "    Skip over non-photo files by checking for common photo extensions (.jpg, .jpeg, .png, etc).\n", "    In this loop you will determine the description of each image one at a time. \n", "    Use `computer.vision.query()` to get a description of the image.\n", "    `computer.vision.query()` takes a `path=` argument to know which photo to describe. \n", "    Print out the description so you can get the full context.\n", "    Determine which sub-directory the photo should go in to.\n", "    Every photo needs to go into one of the sub-directories.\n", "    Make sure you actually move the photo. \n", "    Your task is done when every photo in the unorganized photos directory has been moved to another directory. \n", "    **Confirm that the unorganized photos directory has no more photos**.\n", "    \"\"\"\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Save the profile with a descriptive name. Then run interpreter with:\n", "\n", "`interpreter --profile <profileName.py>`\n", "\n", "Then ask it to organize the directory:\n", "\n", "`Please organize this directory: /path/to/directory`"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}
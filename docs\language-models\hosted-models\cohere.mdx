---
title: Cohere
---

To use Open Interpreter with a model from <PERSON>here, set the `model` flag:

<CodeGroup>

```bash Terminal
interpreter --model command-nightly
```

```python Python
from interpreter import interpreter

interpreter.llm.model = "command-nightly"
interpreter.chat()
```

</CodeGroup>

# Supported Models

We support any model on [Cohere's models page:](https://www.cohere.ai/models)

<CodeGroup>

```bash Terminal
interpreter --model command
interpreter --model command-light
interpreter --model command-medium
interpreter --model command-medium-beta
interpreter --model command-xlarge-beta
interpreter --model command-nightly
```

```python Python
interpreter.llm.model = "command"
interpreter.llm.model = "command-light"
interpreter.llm.model = "command-medium"
interpreter.llm.model = "command-medium-beta"
interpreter.llm.model = "command-xlarge-beta"
interpreter.llm.model = "command-nightly"
```

</CodeGroup>

# Required Environment Variables

Set the following environment variables [(click here to learn how)](https://chat.openai.com/share/1062cdd8-62a1-4aa8-8ec9-eca45645971a) to use these models.

| Environment Variable  | Description  | Where to Find  |
| --------------------- | ------------ | -------------- |
| `COHERE_API_KEY`       | The API key for authenticating to Cohere's services. | [Cohere Account Page](https://app.cohere.ai/login) |
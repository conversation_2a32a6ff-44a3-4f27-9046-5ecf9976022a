# Safe Mode

**⚠️ Safe mode is experimental and does not provide any guarantees of safety or security.**

Open Interpreter is working on providing an experimental safety toolkit to help you feel more confident running the code generated by Open Interpreter.

Install Open Interpreter with the safety toolkit dependencies as part of the bundle:

```shell
pip install open-interpreter[safe]
```

Alternatively, you can install the safety toolkit dependencies separately in your virtual environment:

```shell
pip install semgrep
```

## Features

- **No Auto Run**: Safe mode disables the ability to automatically execute code
- **Code Scanning**: Scan generated code for vulnerabilities with [`semgrep`](https://semgrep.dev/)

## Enabling Safe Mode

You can enable safe mode by passing the `--safe` flag when invoking `interpreter` or by configuring `safe_mode` in your [config file](https://github.com/OpenInterpreter/open-interpreter#configuration).

The safe mode setting has three options:

- `off`: disables the safety toolkit (_default_)
- `ask`: prompts you to confirm that you want to scan code
- `auto`: automatically scans code

### Example Config:

```yaml
model: gpt-4
temperature: 0
verbose: false
safe_mode: ask
```

## Roadmap

Some upcoming features that enable even more safety:

- [Execute code in containers](https://github.com/OpenInterpreter/open-interpreter/pull/459)

## Tips & Tricks

You can adjust the `system_message` in your [config file](https://github.com/OpenInterpreter/open-interpreter#configuration) to include instructions for the model to scan packages with [`guarddog`]() before installing them.

```yaml
model: gpt-4
verbose: false
safe_mode: ask
system_message: |
  # normal system message here
  BEFORE INSTALLING ANY PACKAGES WITH pip OR npm YOU MUST SCAN THEM WITH `guarddog` FIRST. Run `guarddog pypi scan $package` for pip packages and `guarddog npm scan $package` for npm packages. `guarddog` only accepts one package name at a time.
```

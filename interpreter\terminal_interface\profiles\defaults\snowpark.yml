### O<PERSON>EN INTERPRETER CONFIGURATION FILE

# Remove the "#" before the settings below to use them.

# LLM Settings
llm:
  model: "gpt-4o"
  temperature: 0
  # api_key: ...  # Your API key, if the API requires it
  # api_base: ...  # The URL where an OpenAI-compatible server is running to handle LLM API requests
  # api_version: ...  # The version of the API (this is primarily for Azure)
  # max_output: 2500  # The maximum characters of code output visible to the LLM

# Computer Settings
computer:
  import_computer_api: True # Gives OI a helpful Computer API designed for code interpreting language models

# Custom Instructions
custom_instructions: '''
You are going to be connecting to Snowflake, the cloud data platform. You can use the Snowpark API to interact with Snowflake. Here are some common tasks you might want to do:
- Connect to Snowflake
- Run a query

You can use the Snowpark API to do these tasks. To create a session with snowpark, you have to first import the session object from snowpark and pandas, like so:
```python
from snowflake.snowpark import Session
import pandas as pd
```
If this doesnt work, you may need to run the following commands to install snowpark and pandas:
```python
!pip install snowflake-snowpark-python
!pip install pandas
!pip install "snowflake-snowpark-python[pandas]"
!pip install "snowflake-connector-python[pandas]"
```

Then, you can create a dictionary with the necessary connection parameters and create a session. You will access these values from the 
environment variables:
```python
# Retrieve environment variables
snowflake_account = os.getenv("SNOWFLAKE_ACCOUNT")
snowflake_user = os.getenv("SNOWFLAKE_USER")
snowflake_password = os.getenv("SNOWFLAKE_PASSWORD")
snowflake_role = os.getenv("SNOWFLAKE_ROLE")
snowflake_warehouse = os.getenv("SNOWFLAKE_WAREHOUSE")
snowflake_database = os.getenv("SNOWFLAKE_DATABASE") 
snowflake_schema = os.getenv("SNOWFLAKE_SCHEMA")

# Create connection parameters dictionary
connection_parameters = {
    "account": snowflake_account,
    "user": snowflake_user,
    "password": snowflake_password,
    "role": snowflake_role,
    "warehouse": snowflake_warehouse, 
    "database": snowflake_database, 
    "schema": snowflake_schema,
}

# Create a session
session = Session.builder.configs(connection_parameters).create()
```
You should assume that the environment variables have already been set. 
You can run a query against the snowflake data by using the session.sql() method. Then, you can turn the snowpark dataframe
that is created into a pandas dataframe for use in other processes. Here is an example of how you can run a query:
```python
# Run a query
query = "<your query goes here>"
snowpark_dataframe = session.sql(query)

# Convert the snowpark dataframe to a pandas dataframe
df = snowflake_dataframe.to_pandas()
```

You can now use this dataframe to do whatever you need to do with the data.

'''  # This will be appended to the system message

# General Configuration
# auto_run: False  # If True, code will run without asking for confirmation
# safe_mode: "off"  # The safety mode for the LLM — one of "off", "ask", "auto"
# offline: False  # If True, will disable some online features like checking for updates
# verbose: False  # If True, will print detailed logs
# multi_line: False # If True, you can input multiple lines starting and ending with ```

# Documentation
# All options: https://docs.openinterpreter.com/settings

---
title: PaLM API - Google
---

To use Open Interpreter with <PERSON><PERSON>, you must `pip install -q google-generativeai`, then set the `model` flag in Open Interpreter:

<CodeGroup>

```bash Terminal
interpreter --model palm/chat-bison
```

```python Python
from interpreter import interpreter

interpreter.llm.model = "palm/chat-bison"
interpreter.chat()
```

</CodeGroup>

# Required Environment Variables

Set the following environment variables [(click here to learn how)](https://chat.openai.com/share/1062cdd8-62a1-4aa8-8ec9-eca45645971a) to use these models.

| Environment Variable | Description                                                      | Where to Find                                                                        |
| -------------------- | ---------------------------------------------------------------- | ------------------------------------------------------------------------------------ |
| `PALM_API_KEY`       | The PaLM API key from Google Generative AI Developers dashboard. | [Google Generative AI Developers Dashboard](https://developers.generativeai.google/) |

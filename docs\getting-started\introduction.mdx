---
title: Introduction
description: A new way to use computers
---

# <div class="hidden">Introduction</div>

<img src="https://openinterpreter.com/assets/banner.jpg" alt="thumbnail" style={{transform: "translateY(-1.25rem)"}} />

**Open Interpreter** lets language models run code.

You can chat with Open Interpreter through a ChatGPT-like interface in your terminal by running `interpreter` after installing.

This provides a natural-language interface to your computer's general-purpose capabilities:

-   Create and edit photos, videos, PDFs, etc.
-   Control a Chrome browser to perform research
-   Plot, clean, and analyze large datasets
-   ...etc.

<br/>

<Info>You can also build Open Interpreter into your applications with [our Python package.](/usage/python/arguments)</Info>

---

<h1><span class="font-semibold">Quick start</span></h1>

If you already use Python, you can install Open Interpreter via `pip`:

<Steps>
  <Step title="Install" icon={"arrow-down"} iconType={"solid"}>
```bash
pip install open-interpreter
```
  </Step>
  <Step title="Use" icon={"circle"} iconType={"solid"}>
```bash
interpreter
```
  </Step>
</Steps>

We've also developed [one-line installers](/getting-started/setup#experimental-one-line-installers) that install Python and set up Open Interpreter.

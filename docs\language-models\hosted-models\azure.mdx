---
title: Azure
---

To use a model from Azure, set the `model` flag to begin with `azure/`:

<CodeGroup>

```bash Terminal
interpreter --model azure/<your_deployment_id>
```

```python Python
from interpreter import interpreter

interpreter.llm.model = "azure/<your_deployment_id>"
interpreter.chat()
```

</CodeGroup>

# Required Environment Variables

Set the following environment variables [(click here to learn how)](https://chat.openai.com/share/1062cdd8-62a1-4aa8-8ec9-eca45645971a) to use these models.

| Environment Variable  | Description  | Where to Find  |
| --------------------- | ------------ | -------------- |
| `AZURE_API_KEY`       | The API key for authenticating to Azure's services. | [Azure Account Page](https://portal.azure.com/#blade/Microsoft_AAD_IAM/ActiveDirectoryMenuBlade/RegisteredApps) |
| `AZURE_API_BASE`      | The base URL for Azure's services. | [Azure Account Page](https://portal.azure.com/#blade/Microsoft_AAD_IAM/ActiveDirectoryMenuBlade/RegisteredApps) |
| `AZURE_API_VERSION`   | The version of Azure's services. | [Azure Account Page](https://portal.azure.com/#blade/Microsoft_AAD_IAM/ActiveDirectoryMenuBlade/RegisteredApps) |